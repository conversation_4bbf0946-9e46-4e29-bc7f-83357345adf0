<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import AppLayout from '../components/layout/AppLayout.vue'
import DataCard from '../components/common/DataCard.vue'
import LineChart from '../components/charts/LineChart.vue'
import { financeAPI } from '../utils/api'
import { formatCurrency, formatPercent, formatDate } from '../utils/format'
import { ElMessage } from 'element-plus'

// 加载状态
const loading = ref(true)

// 外币资产详情对话框
const assetDialog = ref(false)
const showAllAssets = ref(false)
const displayAssetCount = ref(3) // 默认显示的资产数量

// 新增远期合约对话框
const newContractDialog = ref(false)
const newContractForm = reactive({
  contractNo: '',
  description: '',
  contractType: '买入',
  currency: 'USD',
  amount: 0,
  contractRate: 0,
  marketRate: 0,
  dueDate: '',
  remainingDays: 30,
  counterparty: '',
  purpose: '',
  hedgeRatio: 0.5
})

// 远期合约表单规则
const contractRules = {
  amount: [{ required: true, message: '请输入合约金额', trigger: 'blur' }],
  contractRate: [{ required: true, message: '请输入约定汇率', trigger: 'blur' }],
  dueDate: [{ required: true, message: '请选择到期日', trigger: 'blur' }],
  counterparty: [{ required: true, message: '请输入交易对手', trigger: 'blur' }]
}

// 财务仪表盘数据
const financeData = reactive({
  summary: {
    fxAssetValue: 0,
    fxAssetChange: '',
    fxAssetIncreased: true,

    forwardValue: 0,
    forwardChange: '',
    forwardIncreased: true,

    realizedValue: 0,
    realizedChange: '',
    realizedIncreased: true,

    cashFlowValue: 0,
    cashFlowChange: '',
    cashFlowIncreased: false
  },
  exchangeGain: {
    labels: [],
    values: [],
    period: 'month'
  },
  exchangeRate: {
    currencyPair: '',
    dates: [],
    rates: []
  },
  currencyAssets: [],
  forwardContracts: [],
  realizedGainDetails: [],
  cashFlowDetail: null,
  dataInput: null
})

// 汇兑损益分布图表数据
const exchangeGainChartData = ref({
  labels: [],
  datasets: [
    {
      label: '汇兑损益',
      backgroundColor: (context) => {
        const value = context.dataset.data[context.dataIndex]
        return value >= 0 ? 'rgba(0, 180, 42, 0.7)' : 'rgba(245, 63, 63, 0.7)'
      },
      data: []
    }
  ]
})

// 汇率趋势图表数据
const exchangeRateChartData = ref({
  labels: [],
  datasets: [
    {
      label: '汇率',
      borderColor: '#165DFF',
      backgroundColor: 'rgba(22, 93, 255, 0.1)',
      data: [],
      fill: true
    }
  ]
})

// 期间选择
const period = ref('month')
const periods = [
  { label: '本月', value: 'month' },
  { label: '季度', value: 'quarter' },
  { label: '年度', value: 'year' }
]

// 货币对选择
const currencyPair = ref('USD/CNY')
const currencyPairs = [
  { label: 'USD/CNY', value: 'USD/CNY' },
  { label: 'EUR/CNY', value: 'EUR/CNY' },
  { label: 'JPY/CNY', value: 'JPY/CNY' },
  { label: 'GBP/CNY', value: 'GBP/CNY' }
]

// 远期合约筛选
const contractFilter = ref('all')

// 筛选后的远期合约
const filteredContracts = computed(() => {
  if (contractFilter.value === 'all') {
    return financeData.forwardContracts
  }
  return financeData.forwardContracts.filter(contract => contract.contractType === contractFilter.value)
})

// 为远期合约表格行设置样式
const contractRowClassName = ({ row }) => {
  if (row.isGain) {
    return 'contract-row-gain'
  } else {
    return 'contract-row-loss'
  }
}

// 获取财务仪表盘数据
const fetchFinanceData = async () => {
  loading.value = true
  try {
    const data = await financeAPI.getFinanceDashboard()
    Object.assign(financeData, data)

    // 更新图表数据
    updateExchangeGainChart()
    updateExchangeRateChart()

    loading.value = false
  } catch (error) {
    console.error('获取财务仪表盘数据失败:', error)
    loading.value = false
  }
}

// 获取汇兑损益分布数据
const fetchExchangeGainData = async (periodValue) => {
  try {
    const data = await financeAPI.getExchangeGain({ period: periodValue })
    financeData.exchangeGain = data
    updateExchangeGainChart()
  } catch (error) {
    console.error('获取汇兑损益分布数据失败:', error)
  }
}

// 获取汇率趋势数据
const fetchExchangeRateData = async (pairValue) => {
  try {
    const data = await financeAPI.getExchangeRate({ currencyPair: pairValue })
    financeData.exchangeRate = data
    updateExchangeRateChart()
  } catch (error) {
    console.error('获取汇率趋势数据失败:', error)
  }
}

// 更新汇兑损益图表
const updateExchangeGainChart = () => {
  exchangeGainChartData.value = {
    labels: financeData.exchangeGain.labels,
    datasets: [
      {
        label: '汇兑损益',
        backgroundColor: (context) => {
          const value = financeData.exchangeGain.values[context.dataIndex]
          return value >= 0 ? 'rgba(0, 180, 42, 0.7)' : 'rgba(245, 63, 63, 0.7)'
        },
        data: financeData.exchangeGain.values
      }
    ]
  }
}

// 更新汇率趋势图表
const updateExchangeRateChart = () => {
  exchangeRateChartData.value = {
    labels: financeData.exchangeRate.dates,
    datasets: [
      {
        label: financeData.exchangeRate.currencyPair,
        borderColor: '#165DFF',
        backgroundColor: 'rgba(22, 93, 255, 0.1)',
        data: financeData.exchangeRate.rates,
        fill: true
      }
    ]
  }
}

// 期间切换
const handlePeriodChange = (value) => {
  period.value = value
  fetchExchangeGainData(value)
}

// 货币对切换
const handleCurrencyPairChange = (value) => {
  currencyPair.value = value
  fetchExchangeRateData(value)
}

// 获取已实现损益总额
const getTotalGainLoss = () => {
  if (!financeData.realizedGainDetails || financeData.realizedGainDetails.length === 0) {
    return 0
  }
  return financeData.realizedGainDetails.reduce((sum, item) => sum + item.gainLoss, 0)
}

// 获取汇率变化百分比
const getRateChangePercent = (row) => {
  return Math.abs((row.settlementRate - row.initialRate) / row.initialRate * 100)
}

// 为已实现损益表格行设置样式
const realizedGainRowClass = ({ row }) => {
  if (row.isGain) {
    return 'realized-gain-row-positive'
  } else {
    return 'realized-gain-row-negative'
  }
}

// 获取现金流入总额
const getTotalInflowAmount = () => {
  if (!financeData.cashFlowDetail || !financeData.cashFlowDetail.inflows) {
    return 0
  }
  return new Intl.NumberFormat('zh-CN').format(
    financeData.cashFlowDetail.inflows.reduce((sum, item) => sum + item.amount, 0)
  )
}

// 获取现金流出总额
const getTotalOutflowAmount = () => {
  if (!financeData.cashFlowDetail || !financeData.cashFlowDetail.outflows) {
    return 0
  }
  return new Intl.NumberFormat('zh-CN').format(
    financeData.cashFlowDetail.outflows.reduce((sum, item) => sum + item.amount, 0)
  )
}

// 数据输入面板相关
const activeSection = ref('fx-asset')
const contractType = ref('买入')

// 设置当前活动的输入区域
const setActiveSection = (section) => {
  activeSection.value = section
}

// 计算外币资产预计汇兑损益
const getFxAssetGainPreview = () => {
  const input = financeData.dataInput.fxAssetInput
  if (!input.balance || !input.initialRate || !input.finalRate) {
    return 0
  }
  const initialAmount = input.balance * input.initialRate
  const finalAmount = input.balance * input.finalRate
  return finalAmount - initialAmount
}

// 计算远期合约预计估值损益
const getForwardGainPreview = () => {
  const input = financeData.dataInput.forwardInput
  if (!input.amount || !input.contractRate || !input.currentForwardRate) {
    return 0
  }

  // 买入远期：锁定将来买入外币的汇率，当前汇率高于合约汇率为有利
  // 卖出远期：锁定将来卖出外币的汇率，当前汇率低于合约汇率为有利
  if (contractType.value === '买入') {
    return input.amount * (input.currentForwardRate - input.contractRate)
  } else {
    return input.amount * (input.contractRate - input.currentForwardRate)
  }
}

// 计算已实现损益预计结算损益
const getRealizedGainPreview = () => {
  const input = financeData.dataInput.realizedInput
  if (!input.settlementAmount || !input.initialRate || !input.settlementRate) {
    return 0
  }
  return input.settlementAmount * (input.settlementRate - input.initialRate)
}

// 计算现金流量等值人民币
const getCashFlowCnyPreview = () => {
  const input = financeData.dataInput.cashFlowInput
  if (!input.amount || !input.exchangeRate) {
    return 0
  }
  return input.amount * input.exchangeRate
}

// 获取已实现损益详情
const fetchRealizedGainDetails = async () => {
  try {
    const data = await financeAPI.getRealizedGainDetails()
    financeData.realizedGainDetails = data
  } catch (error) {
    console.error('获取已实现损益详情失败:', error)
  }
}

// 获取现金流量详情
const fetchCashFlowDetail = async () => {
  try {
    const data = await financeAPI.getCashFlowDetail()
    financeData.cashFlowDetail = data
  } catch (error) {
    console.error('获取现金流量详情失败:', error)
  }
}

// 获取数据输入面板配置
const fetchDataInputConfig = async () => {
  try {
    const data = await financeAPI.getDataInputConfig()
    financeData.dataInput = data
  } catch (error) {
    console.error('获取数据输入面板配置失败:', error)
  }
}

// 重新计算财务数据
const handleRecalculate = async () => {
  if (!financeData.dataInput) return

  loading.value = true
  try {
    const data = await financeAPI.recalculateFinanceData(financeData.dataInput)
    Object.assign(financeData, data)

    // 更新图表数据
    updateExchangeGainChart()
    updateExchangeRateChart()

    loading.value = false
  } catch (error) {
    console.error('重新计算财务数据失败:', error)
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchFinanceData()
  fetchRealizedGainDetails()
  fetchCashFlowDetail()
  fetchDataInputConfig()
})

// 显示所有外币资产
const toggleShowAllAssets = () => {
  showAllAssets.value = !showAllAssets.value
}

// 打开资产详情对话框
const openAssetDialog = () => {
  assetDialog.value = true
}

// 显示的资产列表
const displayedAssets = computed(() => {
  if (showAllAssets.value) {
    return financeData.currencyAssets
  } else {
    return financeData.currencyAssets.slice(0, displayAssetCount.value)
  }
})

// 打开新增远期合约对话框
const openNewContractDialog = () => {
  // 重置表单数据
  Object.assign(newContractForm, {
    contractNo: 'FX' + new Date().getTime().toString().slice(-6),
    description: '',
    contractType: '买入',
    currency: 'USD',
    amount: 0,
    contractRate: 0,
    marketRate: 0,
    dueDate: '',
    remainingDays: 30,
    counterparty: '',
    purpose: '',
    hedgeRatio: 0.5
  })

  newContractDialog.value = true
}

// 提交新增远期合约表单
const contractFormRef = ref(null)
const submitNewContract = () => {
  if (!contractFormRef.value) return

  contractFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 计算估值
        const valuationGain = calculateContractGain(newContractForm)

        // 创建新合约对象
        const newContract = {
          ...newContractForm,
          cnyAmount: Math.round(newContractForm.amount * newContractForm.contractRate),
          valuationGain,
          isGain: valuationGain > 0,
          status: '待确认'
        }

        // 调用API添加新合约
        // const result = await financeAPI.addForwardContract(newContract)

        // 添加到本地列表
        financeData.forwardContracts.unshift(newContract)

        // 关闭对话框
        newContractDialog.value = false
        loading.value = false

        // 显示成功消息
        ElMessage({
          type: 'success',
          message: '新增远期合约成功'
        })
      } catch (error) {
        console.error('新增远期合约失败:', error)
        loading.value = false

        // 显示错误消息
        ElMessage({
          type: 'error',
          message: '新增远期合约失败: ' + error.message
        })
      }
    }
  })
}

// 计算远期合约估值损益
const calculateContractGain = (contract) => {
  if (contract.contractType === '买入') {
    // 买入远期：市场汇率高于合约汇率时有利
    return Math.round(contract.amount * (contract.marketRate - contract.contractRate))
  } else {
    // 卖出远期：市场汇率低于合约汇率时有利
    return Math.round(contract.amount * (contract.contractRate - contract.marketRate))
  }
}
</script>

<template>
  <AppLayout>
    <div class="finance-dashboard">
      <!-- 概览卡片 -->
      <div class="summary-cards">
        <!-- 外币资产卡片 -->
        <DataCard title="外币资产" :loading="loading">
          <div class="summary-card">
            <div class="card-icon fx-asset">
              <i class="fas fa-money-bill"></i>
            </div>
            <div class="card-content">
              <div class="card-value">
                <span class="currency">¥</span>
                {{ new Intl.NumberFormat('zh-CN').format(financeData.summary.fxAssetValue) }}
              </div>
              <div
                class="card-change"
                :class="{ 'increased': financeData.summary.fxAssetIncreased, 'decreased': !financeData.summary.fxAssetIncreased }"
              >
                <i :class="financeData.summary.fxAssetIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ financeData.summary.fxAssetChange }}
              </div>
            </div>
          </div>
        </DataCard>

        <!-- 远期合约卡片 -->
        <DataCard title="远期合约" :loading="loading">
          <div class="summary-card">
            <div class="card-icon forward">
              <i class="fas fa-calendar-check"></i>
            </div>
            <div class="card-content">
              <div class="card-value">
                <span class="currency">¥</span>
                {{ new Intl.NumberFormat('zh-CN').format(financeData.summary.forwardValue) }}
              </div>
              <div
                class="card-change"
                :class="{ 'increased': financeData.summary.forwardIncreased, 'decreased': !financeData.summary.forwardIncreased }"
              >
                <i :class="financeData.summary.forwardIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ financeData.summary.forwardChange }}
              </div>
            </div>
          </div>
        </DataCard>

        <!-- 已实现损益卡片 -->
        <DataCard title="已实现损益" :loading="loading">
          <div class="summary-card">
            <div class="card-icon realized">
              <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="card-content">
              <div class="card-value">
                <span class="currency">¥</span>
                {{ new Intl.NumberFormat('zh-CN').format(financeData.summary.realizedValue) }}
              </div>
              <div
                class="card-change"
                :class="{ 'increased': financeData.summary.realizedIncreased, 'decreased': !financeData.summary.realizedIncreased }"
              >
                <i :class="financeData.summary.realizedIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ financeData.summary.realizedChange }}
              </div>
            </div>
          </div>
        </DataCard>

        <!-- 现金流量卡片 -->
        <DataCard title="现金流量影响" :loading="loading">
          <div class="summary-card">
            <div class="card-icon cash-flow">
              <i class="fas fa-cash-register"></i>
            </div>
            <div class="card-content">
              <div class="card-value">
                <span class="currency">¥</span>
                {{ new Intl.NumberFormat('zh-CN').format(financeData.summary.cashFlowValue) }}
              </div>
              <div
                class="card-change"
                :class="{ 'increased': financeData.summary.cashFlowIncreased, 'decreased': !financeData.summary.cashFlowIncreased }"
              >
                <i :class="financeData.summary.cashFlowIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ financeData.summary.cashFlowChange }}
              </div>
            </div>
          </div>
        </DataCard>
      </div>

      <!-- 图表区域 -->
      <div class="chart-section">
        <!-- 汇兑损益分布图表 -->
        <DataCard title="汇兑损益分布" :loading="loading" class="gain-chart">
          <div class="chart-header">
            <div class="chart-period">
              <el-radio-group v-model="period" @change="handlePeriodChange">
                <el-radio-button
                  v-for="item in periods"
                  :key="item.value"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <LineChart :chart-data="exchangeGainChartData" :height="300" />
        </DataCard>

        <!-- 汇率趋势图表 -->
        <DataCard title="汇率趋势" :loading="loading" class="rate-chart">
          <div class="chart-header">
            <el-select v-model="currencyPair" @change="handleCurrencyPairChange" size="small">
              <el-option
                v-for="item in currencyPairs"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <LineChart :chart-data="exchangeRateChartData" :height="300" />
        </DataCard>
      </div>

      <!-- 详细数据区域 -->
      <div class="detail-section">
        <!-- 外币资产详情 -->
        <DataCard title="外币资产详情" id="fx-asset" :loading="loading">
          <div class="fx-asset-header">
            <div class="asset-summary">
              <div class="summary-item">
                <span class="summary-label">总资产价值</span>
                <span class="summary-value">¥{{ new Intl.NumberFormat('zh-CN').format(financeData.summary.fxAssetValue) }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">汇兑损益</span>
                <span
                  class="summary-value"
                  :class="{ 'increased': financeData.summary.fxAssetIncreased, 'decreased': !financeData.summary.fxAssetIncreased }"
                >
                  {{ financeData.summary.fxAssetChange }}
                </span>
              </div>
            </div>
            <div class="asset-actions">
              <el-button size="small" type="primary" @click="openAssetDialog">
                <i class="fas fa-chart-bar"></i> 详细分析
              </el-button>
            </div>
          </div>

          <div class="asset-cards-grid">
            <div
              v-for="(item, index) in displayedAssets"
              :key="index"
              class="asset-card"
              :class="{ 'asset-gain': item.isGain, 'asset-loss': !item.isGain }"
            >
              <div class="asset-card-header">
                <div class="asset-title">
                  <h4>{{ item.name }}</h4>
                  <div class="asset-tags">
                    <el-tag
                      size="small"
                      :type="item.riskLevel === '低' ? 'success' : item.riskLevel === '中' ? 'warning' : 'danger'"
                      effect="plain"
                    >
                      {{ item.riskLevel }}风险
                    </el-tag>
                    <el-tag size="small" type="info" effect="plain">{{ item.category }}</el-tag>
                  </div>
                </div>
                <div class="asset-amount">
                  <div class="amount-primary">{{ new Intl.NumberFormat('zh-CN').format(item.amount) }} {{ item.currency }}</div>
                  <div class="amount-secondary">≈ ¥{{ new Intl.NumberFormat('zh-CN').format(item.cnyAmount) }}</div>
                </div>
              </div>

              <div class="asset-card-body">
                <div class="rate-comparison">
                  <div class="rate-item">
                    <span class="rate-label">期初汇率</span>
                    <span class="rate-value">{{ item.initialRate }}</span>
                  </div>
                  <div class="rate-arrow">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                  <div class="rate-item">
                    <span class="rate-label">期末汇率</span>
                    <span class="rate-value">{{ item.finalRate }}</span>
                  </div>
                </div>

                <div class="gain-loss-section">
                  <div class="gain-loss-item">
                    <span class="gain-loss-label">汇兑损益</span>
                    <span
                      class="gain-loss-value"
                      :class="{ 'increased': item.isGain, 'decreased': !item.isGain }"
                    >
                      {{ item.isGain ? '+' : '' }}{{ new Intl.NumberFormat('zh-CN').format(item.exchangeGain) }} CNY
                    </span>
                  </div>
                  <div class="gain-loss-percentage">
                    <span
                      :class="{ 'increased': item.isGain, 'decreased': !item.isGain }"
                    >
                      {{ item.isGain ? '+' : '' }}{{ ((item.exchangeGain / item.initialCnyAmount) * 100).toFixed(2) }}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="asset-actions-footer">
            <button
              v-if="financeData.currencyAssets.length > displayAssetCount.value && !showAllAssets"
              class="action-btn secondary"
              @click="toggleShowAllAssets"
            >
              <i class="fas fa-chevron-down"></i> 查看更多
            </button>
            <button
              v-else-if="showAllAssets"
              class="action-btn secondary"
              @click="toggleShowAllAssets"
            >
              <i class="fas fa-chevron-up"></i> 收起
            </button>
          </div>
        </DataCard>

        <!-- 外币资产详情对话框 -->
        <el-dialog
          v-model="assetDialog"
          title="外币资产详细分析"
          width="80%"
          destroy-on-close
        >
          <div class="asset-dialog-content">
            <div class="asset-summary-cards">
              <div class="summary-card">
                <div class="card-title">总外币资产</div>
                <div class="card-value">
                  <span class="currency">¥</span>
                  {{ new Intl.NumberFormat('zh-CN').format(financeData.summary.fxAssetValue) }}
                </div>
                <div
                  class="card-change"
                  :class="{ 'increased': financeData.summary.fxAssetIncreased, 'decreased': !financeData.summary.fxAssetIncreased }"
                >
                  <i :class="financeData.summary.fxAssetIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                  {{ financeData.summary.fxAssetChange }}
                </div>
              </div>
              <div class="summary-card">
                <div class="card-title">资产组成</div>
                <div class="pie-chart-placeholder">
                  <!-- 这里可以放置一个饼图展示不同币种的占比 -->
                </div>
              </div>
            </div>

            <el-table :data="financeData.currencyAssets" style="width: 100%" border stripe>
              <el-table-column label="资产名称">
                <template #default="scope">
                  <div class="asset-table-name">
                    <span>{{ scope.row.name }}</span>
                    <div class="asset-description">{{ scope.row.description }}</div>
                  </div>
                  <div class="asset-tags">
                    <el-tag size="small" :type="scope.row.riskLevel === '低' ? 'success' : scope.row.riskLevel === '中' ? 'warning' : 'danger'">
                      {{ scope.row.riskLevel }}风险
                    </el-tag>
                    <el-tag size="small" type="info" class="ml-2">{{ scope.row.category }}</el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="币种/金额" width="180">
                <template #default="scope">
                  <div class="asset-amount">
                    <div class="currency-code">{{ scope.row.currency }}</div>
                    <div class="amount-value">{{ new Intl.NumberFormat('zh-CN').format(scope.row.amount) }}</div>
                  </div>
                  <div class="asset-cny">≈ ¥ {{ new Intl.NumberFormat('zh-CN').format(scope.row.cnyAmount) }}</div>
                </template>
              </el-table-column>
              <el-table-column label="期初汇率/估值" width="180">
                <template #default="scope">
                  <div>{{ scope.row.initialRate }}</div>
                  <div class="rate-value">¥ {{ new Intl.NumberFormat('zh-CN').format(scope.row.initialCnyAmount) }}</div>
                </template>
              </el-table-column>
              <el-table-column label="期末汇率/估值" width="180">
                <template #default="scope">
                  <div>{{ scope.row.finalRate }}</div>
                  <div class="rate-value">¥ {{ new Intl.NumberFormat('zh-CN').format(scope.row.finalCnyAmount) }}</div>
                </template>
              </el-table-column>
              <el-table-column label="汇兑损益" width="180">
                <template #default="scope">
                  <div
                    class="exchange-gain"
                    :class="{ 'increased': scope.row.isGain, 'decreased': !scope.row.isGain }"
                  >
                    {{ scope.row.isGain ? '+' : '' }}{{ new Intl.NumberFormat('zh-CN').format(scope.row.exchangeGain) }}
                  </div>
                  <div
                    class="rate-percentage"
                    :class="{ 'increased': scope.row.isGain, 'decreased': !scope.row.isGain }"
                  >
                    {{ scope.row.isGain ? '+' : '' }}{{ ((scope.row.exchangeGain / scope.row.initialCnyAmount) * 100).toFixed(2) }}%
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="assetDialog = false">关闭</el-button>
              <el-button type="primary" @click="assetDialog = false">
                导出数据
              </el-button>
            </span>
          </template>
        </el-dialog>

        <!-- 远期合约数据 -->
        <DataCard title="远期合约详情" id="forward-contract" :loading="loading">
          <div class="contract-header">
            <div class="contract-summary">
              <div class="summary-item">
                <span class="summary-label">合约总价值</span>
                <span class="summary-value">¥{{ new Intl.NumberFormat('zh-CN').format(financeData.summary.forwardValue) }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">估值损益</span>
                <span
                  class="summary-value"
                  :class="{ 'increased': financeData.summary.forwardIncreased, 'decreased': !financeData.summary.forwardIncreased }"
                >
                  {{ financeData.summary.forwardChange }}
                </span>
              </div>
            </div>
            <div class="contract-actions">
              <el-select v-model="contractFilter" placeholder="筛选" size="small" style="width: 100px; margin-right: 8px;">
                <el-option label="全部" value="all" />
                <el-option label="买入" value="买入" />
                <el-option label="卖出" value="卖出" />
              </el-select>
              <el-button size="small" type="primary" @click="openNewContractDialog">
                <i class="fas fa-plus"></i> 新增合约
              </el-button>
            </div>
          </div>

          <div class="contract-cards-grid">
            <div
              v-for="(contract, index) in filteredContracts"
              :key="index"
              class="contract-card"
              :class="{ 'contract-gain': contract.isGain, 'contract-loss': !contract.isGain }"
            >
              <div class="contract-card-header">
                <div class="contract-title">
                  <h4>{{ contract.contractType }}合约</h4>
                  <div class="contract-tags">
                    <el-tag
                      size="small"
                      :type="contract.contractType === '买入' ? 'success' : 'warning'"
                      effect="plain"
                    >
                      {{ contract.contractType }}
                    </el-tag>
                    <el-tag size="small" type="info" effect="plain">{{ contract.currency }}</el-tag>
                    <el-tag v-if="contract.status" size="small" type="info" effect="plain">{{ contract.status }}</el-tag>
                  </div>
                </div>
                <div class="contract-amount">
                  <div class="amount-primary">{{ new Intl.NumberFormat('zh-CN').format(contract.amount) }} {{ contract.currency }}</div>
                  <div class="amount-secondary" v-if="contract.cnyAmount">≈ ¥{{ new Intl.NumberFormat('zh-CN').format(contract.cnyAmount) }}</div>
                </div>
              </div>

              <div class="contract-card-body">
                <div class="rate-comparison">
                  <div class="rate-item">
                    <span class="rate-label">约定汇率</span>
                    <span class="rate-value">{{ contract.contractRate }}</span>
                  </div>
                  <div class="rate-arrow">
                    <i class="fas fa-exchange-alt"></i>
                  </div>
                  <div class="rate-item">
                    <span class="rate-label">当前远期汇率</span>
                    <span class="rate-value">{{ contract.marketRate }}</span>
                  </div>
                </div>

                <div class="contract-details">
                  <div class="detail-row">
                    <div class="detail-item">
                      <span class="detail-label">剩余期限</span>
                      <span class="detail-value">{{ contract.remainingDays }}天</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">到期日期</span>
                      <span class="detail-value">{{ contract.dueDate }}</span>
                    </div>
                  </div>
                  <div class="detail-row">
                    <div class="detail-item">
                      <span class="detail-label">交易对手</span>
                      <span class="detail-value">{{ contract.counterparty }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">用途</span>
                      <span class="detail-value">{{ contract.purpose }}</span>
                    </div>
                  </div>
                  <div class="detail-row" v-if="contract.hedgeRatio">
                    <div class="detail-item">
                      <span class="detail-label">套保比例</span>
                      <span class="detail-value">{{ (contract.hedgeRatio * 100).toFixed(0) }}%</span>
                    </div>
                  </div>
                </div>

                <div class="valuation-section">
                  <div class="valuation-item">
                    <span class="valuation-label">公允价值</span>
                    <span
                      class="valuation-value"
                      :class="{ 'increased': contract.isGain, 'decreased': !contract.isGain }"
                    >
                      {{ contract.isGain ? '+' : '' }}{{ new Intl.NumberFormat('zh-CN').format(contract.valuationGain) }} CNY
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DataCard>

        <!-- 新增远期合约对话框 -->
        <el-dialog
          v-model="newContractDialog"
          title="新增远期合约"
          width="600px"
          destroy-on-close
        >
          <el-form
            ref="contractFormRef"
            :model="newContractForm"
            :rules="contractRules"
            label-width="100px"
            label-position="left"
            status-icon
          >
            <el-form-item label="合约编号">
              <el-input v-model="newContractForm.contractNo" disabled placeholder="自动生成"></el-input>
            </el-form-item>

            <el-form-item label="合约描述">
              <el-input v-model="newContractForm.description" placeholder="请输入合约描述"></el-input>
            </el-form-item>

            <div class="form-row">
              <el-form-item label="合约类型" prop="contractType" class="form-item-half">
                <el-select v-model="newContractForm.contractType" class="full-width">
                  <el-option label="买入" value="买入"></el-option>
                  <el-option label="卖出" value="卖出"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="币种" prop="currency" class="form-item-half">
                <el-select v-model="newContractForm.currency" class="full-width">
                  <el-option
                    v-for="currency in financeData.dataInput?.currencies || ['USD', 'EUR', 'JPY', 'GBP']"
                    :key="currency"
                    :label="currency"
                    :value="currency"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="合约金额" prop="amount" class="form-item-half">
                <el-input-number
                  v-model="newContractForm.amount"
                  :precision="2"
                  :step="1000"
                  :min="0"
                  controls-position="right"
                  class="full-width"
                ></el-input-number>
              </el-form-item>

              <el-form-item label="约定汇率" prop="contractRate" class="form-item-half">
                <el-input-number
                  v-model="newContractForm.contractRate"
                  :precision="4"
                  :step="0.0001"
                  :min="0"
                  controls-position="right"
                  class="full-width"
                ></el-input-number>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="市场汇率" prop="marketRate" class="form-item-half">
                <el-input-number
                  v-model="newContractForm.marketRate"
                  :precision="4"
                  :step="0.0001"
                  :min="0"
                  controls-position="right"
                  class="full-width"
                ></el-input-number>
              </el-form-item>

              <el-form-item label="到期日期" prop="dueDate" class="form-item-half">
                <el-date-picker
                  v-model="newContractForm.dueDate"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="full-width"
                ></el-date-picker>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="交易对手" prop="counterparty" class="form-item-half">
                <el-input v-model="newContractForm.counterparty" placeholder="请输入交易对手"></el-input>
              </el-form-item>

              <el-form-item label="用途" prop="purpose" class="form-item-half">
                <el-select v-model="newContractForm.purpose" class="full-width">
                  <el-option label="套期保值" value="套期保值"></el-option>
                  <el-option label="投机交易" value="投机交易"></el-option>
                  <el-option label="日常结算" value="日常结算"></el-option>
                </el-select>
              </el-form-item>
            </div>

            <el-form-item label="套保比例" v-if="newContractForm.purpose === '套期保值'">
              <el-slider
                v-model="newContractForm.hedgeRatio"
                :step="0.01"
                :min="0"
                :max="1"
                :format-tooltip="(val) => (val * 100).toFixed(0) + '%'"
              ></el-slider>
            </el-form-item>

            <!-- 估值预览 -->
            <div class="contract-preview" v-if="newContractForm.amount > 0 && newContractForm.contractRate > 0 && newContractForm.marketRate > 0">
              <div class="preview-title">估值预览</div>
              <div class="preview-content">
                <div class="preview-item">
                  <span class="preview-label">合约金额(CNY):</span>
                  <span class="preview-value">¥ {{ new Intl.NumberFormat('zh-CN').format(Math.round(newContractForm.amount * newContractForm.contractRate)) }}</span>
                </div>
                <div class="preview-item">
                  <span class="preview-label">估值损益:</span>
                  <span
                    class="preview-value"
                    :class="{
                      'increased': calculateContractGain(newContractForm) > 0,
                      'decreased': calculateContractGain(newContractForm) < 0
                    }"
                  >
                    {{ calculateContractGain(newContractForm) > 0 ? '+' : '' }}{{ new Intl.NumberFormat('zh-CN').format(calculateContractGain(newContractForm)) }} CNY
                  </span>
                </div>
              </div>
            </div>
          </el-form>

          <template #footer>
            <span class="dialog-footer">
              <el-button @click="newContractDialog = false">取消</el-button>
              <el-button type="primary" @click="submitNewContract" :loading="loading">
                提交
              </el-button>
            </span>
          </template>
        </el-dialog>
      </div>

      <!-- 已实现损益和现金流量区域 -->
      <div class="detail-section">
        <!-- 已实现损益详情 -->
        <DataCard title="已实现损益详情" id="realized-gain" :loading="loading">
          <div class="realized-gain-table">
            <div class="realized-gain-summary">
              <div class="summary-item">
                <span class="summary-label">总结算金额</span>
                <span class="summary-value">{{
                  new Intl.NumberFormat('zh-CN').format(
                    financeData.realizedGainDetails?.reduce((sum, item) => sum + item.amount, 0) || 0
                  )
                }} USD</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">总损益</span>
                <span class="summary-value" :class="{
                  'increased': getTotalGainLoss() >= 0,
                  'decreased': getTotalGainLoss() < 0
                }">
                  {{ getTotalGainLoss() >= 0 ? '+' : '' }}{{
                    new Intl.NumberFormat('zh-CN').format(getTotalGainLoss())
                  }} CNY
                </span>
              </div>
            </div>
            <el-table
              :data="financeData.realizedGainDetails"
              style="width: 100%"
              :row-class-name="realizedGainRowClass"
              stripe
            >
              <el-table-column prop="transactionDate" label="交易日期" width="110">
                <template #default="scope">
                  <div class="transaction-date">{{ scope.row.transactionDate }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="currency" label="币种" width="70" />
              <el-table-column prop="amount" label="金额" width="120">
                <template #default="scope">
                  <div class="amount-display">
                    <div class="amount-value">{{ new Intl.NumberFormat('zh-CN').format(scope.row.amount) }}</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="汇率信息" width="180">
                <template #default="scope">
                  <div class="rate-comparison">
                    <div class="rate-item">
                      <span class="rate-label">初始汇率:</span>
                      <span class="rate-value">{{ scope.row.initialRate }}</span>
                    </div>
                    <div class="rate-item">
                      <span class="rate-label">结算汇率:</span>
                      <span class="rate-value">{{ scope.row.settlementRate }}</span>
                    </div>
                    <div class="rate-change" :class="{
                      'increased': scope.row.settlementRate > scope.row.initialRate,
                      'decreased': scope.row.settlementRate < scope.row.initialRate
                    }">
                      <i :class="scope.row.settlementRate > scope.row.initialRate ? 'el-icon-top' : 'el-icon-bottom'"></i>
                      {{ getRateChangePercent(scope.row).toFixed(2) }}%
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="gainLoss" label="损益" min-width="120">
                <template #default="scope">
                  <span
                    class="gain-loss-value"
                    :class="{ 'increased': scope.row.isGain, 'decreased': !scope.row.isGain }"
                  >
                    {{ scope.row.isGain ? '+' : '' }}{{ new Intl.NumberFormat('zh-CN').format(scope.row.gainLoss) }} CNY
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </DataCard>

        <!-- 现金流量详情 -->
        <DataCard title="现金流量汇率影响" id="cash-flow" :loading="loading">
          <div class="cash-flow-detail" v-if="financeData.cashFlowDetail">
            <div class="cash-flow-summary">
              <div class="cash-flow-chart">
                <div class="flow-chart-item">
                  <div class="flow-chart-line flow-in"></div>
                  <div class="flow-chart-amount">流入: {{ getTotalInflowAmount() }} USD</div>
                </div>
                <div class="flow-chart-item">
                  <div class="flow-chart-line flow-out"></div>
                  <div class="flow-chart-amount">流出: {{ getTotalOutflowAmount() }} USD</div>
                </div>
              </div>
            </div>

            <!-- 期初余额 -->
            <div class="cash-flow-item initial-balance">
              <div class="cash-flow-header">
                <div class="cash-flow-icon">
                  <i class="fas fa-money-bill"></i>
                </div>
                <h4 class="cash-flow-title">期初余额</h4>
                <span class="cash-flow-amount">
                  {{ financeData.cashFlowDetail.initialBalance?.amount }} {{ financeData.cashFlowDetail.initialBalance?.currency }}
                </span>
              </div>
              <div class="cash-flow-details">
                <div class="detail-grid">
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">期初汇率</span>
                      <span class="detail-value">{{ financeData.cashFlowDetail.initialBalance?.exchangeRate }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">人民币金额</span>
                      <span class="detail-value">¥ {{ new Intl.NumberFormat('zh-CN').format(financeData.cashFlowDetail.initialBalance?.cnyAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 本期流入 -->
            <div class="cash-flow-item inflow">
              <div class="cash-flow-header">
                <div class="cash-flow-icon">
                  <i class="fas fa-arrow-down"></i>
                </div>
                <h4 class="cash-flow-title">本期流入</h4>
                <span class="cash-flow-amount">
                  {{ getTotalInflowAmount() }} USD
                </span>
              </div>
              <div class="cash-flow-details">
                <div v-for="(inflow, index) in financeData.cashFlowDetail.inflows" :key="index" class="detail-grid">
                  <div class="detail-col inflow-desc">
                    <div class="detail-row">
                      <span class="detail-value">{{ inflow.description }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-value">{{ new Intl.NumberFormat('zh-CN').format(inflow.amount) }} {{ inflow.currency }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">@ {{ inflow.exchangeRate }}</span>
                      <span class="detail-value">¥ {{ new Intl.NumberFormat('zh-CN').format(inflow.cnyAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 本期流出 -->
            <div class="cash-flow-item outflow">
              <div class="cash-flow-header">
                <div class="cash-flow-icon">
                  <i class="fas fa-arrow-up"></i>
                </div>
                <h4 class="cash-flow-title">本期流出</h4>
                <span class="cash-flow-amount">
                  {{ getTotalOutflowAmount() }} USD
                </span>
              </div>
              <div class="cash-flow-details">
                <div v-for="(outflow, index) in financeData.cashFlowDetail.outflows" :key="index" class="detail-grid">
                  <div class="detail-col outflow-desc">
                    <div class="detail-row">
                      <span class="detail-value">{{ outflow.description }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-value">{{ new Intl.NumberFormat('zh-CN').format(outflow.amount) }} {{ outflow.currency }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">@ {{ outflow.exchangeRate }}</span>
                      <span class="detail-value">¥ {{ new Intl.NumberFormat('zh-CN').format(outflow.cnyAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 期末余额 -->
            <div class="cash-flow-item final-balance">
              <div class="cash-flow-header">
                <div class="cash-flow-icon">
                  <i class="fas fa-wallet"></i>
                </div>
                <h4 class="cash-flow-title">期末余额</h4>
                <span class="cash-flow-amount">
                  {{ financeData.cashFlowDetail.finalBalance?.amount }} {{ financeData.cashFlowDetail.finalBalance?.currency }}
                </span>
              </div>
              <div class="cash-flow-details">
                <div class="detail-grid">
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">期末汇率</span>
                      <span class="detail-value">{{ financeData.cashFlowDetail.finalBalance?.exchangeRate }}</span>
                    </div>
                  </div>
                  <div class="detail-col">
                    <div class="detail-row">
                      <span class="detail-label">人民币金额</span>
                      <span class="detail-value">¥ {{ new Intl.NumberFormat('zh-CN').format(financeData.cashFlowDetail.finalBalance?.cnyAmount) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 汇率变动影响 -->
            <div class="cash-flow-impact" v-if="financeData.cashFlowDetail.exchangeRateImpact">
              <div class="impact-header">
                <div class="impact-icon" :class="{ 'negative-impact': financeData.cashFlowDetail.exchangeRateImpact.isNegative }">
                  <i class="fas fa-exchange-alt"></i>
                </div>
                <h4 class="impact-title">汇率变动影响</h4>
                <span
                  class="impact-amount"
                  :class="{ 'decreased': financeData.cashFlowDetail.exchangeRateImpact.isNegative, 'increased': !financeData.cashFlowDetail.exchangeRateImpact.isNegative }"
                >
                  {{ financeData.cashFlowDetail.exchangeRateImpact.isNegative ? '' : '+' }}{{ new Intl.NumberFormat('zh-CN').format(financeData.cashFlowDetail.exchangeRateImpact.impact) }} CNY
                </span>
              </div>
              <p class="impact-description">{{ financeData.cashFlowDetail.exchangeRateImpact.description }}</p>
            </div>
          </div>
        </DataCard>
      </div>

      <!-- 数据输入面板 -->
      <DataCard title="数据输入面板" :loading="loading" v-if="financeData.dataInput">
        <div class="data-input-panel">
          <div class="panel-header">
            <div class="panel-title">
              <i class="fas fa-sliders-h"></i>
              <span>外汇风险管理数据维护</span>
            </div>
            <el-button type="primary" @click="handleRecalculate" :loading="loading">
              <i class="fas fa-calculator"></i> 重新计算
            </el-button>
          </div>

          <div class="input-grid">
            <!-- 外币资产输入 -->
            <div class="input-section" :class="{'active-section': activeSection === 'fx-asset'}" @click="setActiveSection('fx-asset')">
              <div class="section-header">
                <div class="section-icon fx-asset">
                  <i class="fas fa-money-bill"></i>
                </div>
                <h4>外币资产录入</h4>
              </div>
              <div class="input-form">
                <div class="form-row">
                  <div class="form-item">
                    <label>币种</label>
                    <el-select v-model="financeData.dataInput.fxAssetInput.currency" size="small" class="full-width">
                      <el-option
                        v-for="currency in financeData.dataInput.currencies"
                        :key="currency"
                        :label="currency"
                        :value="currency"
                      />
                    </el-select>
                  </div>
                  <div class="form-item">
                    <label>外币余额</label>
                    <el-input-number
                      v-model="financeData.dataInput.fxAssetInput.balance"
                      :precision="2"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>期初汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.fxAssetInput.initialRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>期末汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.fxAssetInput.finalRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-result" v-if="financeData.dataInput.fxAssetInput.balance">
                  <div class="result-item">
                    <span>预计汇兑损益：</span>
                    <span :class="{
                      'increased': getFxAssetGainPreview() >= 0,
                      'decreased': getFxAssetGainPreview() < 0
                    }">
                      {{ getFxAssetGainPreview() >= 0 ? '+' : '' }}{{ getFxAssetGainPreview().toFixed(2) }} CNY
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 远期合约输入 -->
            <div class="input-section" :class="{'active-section': activeSection === 'forward'}" @click="setActiveSection('forward')">
              <div class="section-header">
                <div class="section-icon forward">
                  <i class="fas fa-calendar-check"></i>
                </div>
                <h4>远期合约录入</h4>
              </div>
              <div class="input-form">
                <div class="form-row">
                  <div class="form-item">
                    <label>币种</label>
                    <el-select v-model="financeData.dataInput.forwardInput.currency" size="small" class="full-width">
                      <el-option
                        v-for="currency in financeData.dataInput.currencies"
                        :key="currency"
                        :label="currency"
                        :value="currency"
                      />
                    </el-select>
                  </div>
                  <div class="form-item">
                    <label>类型</label>
                    <el-select v-model="contractType" size="small" class="full-width">
                      <el-option label="买入" value="买入" />
                      <el-option label="卖出" value="卖出" />
                    </el-select>
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>合约金额</label>
                    <el-input-number
                      v-model="financeData.dataInput.forwardInput.amount"
                      :precision="2"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>约定汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.forwardInput.contractRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>当前远期汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.forwardInput.currentForwardRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>剩余期限(月)</label>
                    <el-input-number
                      v-model="financeData.dataInput.forwardInput.remainingMonths"
                      :min="1"
                      :max="24"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-result" v-if="financeData.dataInput.forwardInput.amount">
                  <div class="result-item">
                    <span>预计估值损益：</span>
                    <span :class="{
                      'increased': getForwardGainPreview() >= 0,
                      'decreased': getForwardGainPreview() < 0
                    }">
                      {{ getForwardGainPreview() >= 0 ? '+' : '' }}{{ getForwardGainPreview().toFixed(2) }} CNY
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 已实现损益输入 -->
            <div class="input-section" :class="{'active-section': activeSection === 'realized'}" @click="setActiveSection('realized')">
              <div class="section-header">
                <div class="section-icon realized">
                  <i class="fas fa-exchange-alt"></i>
                </div>
                <h4>已实现损益录入</h4>
              </div>
              <div class="input-form">
                <div class="form-row">
                  <div class="form-item">
                    <label>币种</label>
                    <el-select v-model="financeData.dataInput.realizedInput.currency" size="small" class="full-width">
                      <el-option
                        v-for="currency in financeData.dataInput.currencies"
                        :key="currency"
                        :label="currency"
                        :value="currency"
                      />
                    </el-select>
                  </div>
                  <div class="form-item">
                    <label>结算金额</label>
                    <el-input-number
                      v-model="financeData.dataInput.realizedInput.settlementAmount"
                      :precision="2"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>初始汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.realizedInput.initialRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>结算汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.realizedInput.settlementRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-result" v-if="financeData.dataInput.realizedInput.settlementAmount">
                  <div class="result-item">
                    <span>预计结算损益：</span>
                    <span :class="{
                      'increased': getRealizedGainPreview() >= 0,
                      'decreased': getRealizedGainPreview() < 0
                    }">
                      {{ getRealizedGainPreview() >= 0 ? '+' : '' }}{{ getRealizedGainPreview().toFixed(2) }} CNY
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 现金流量输入 -->
            <div class="input-section" :class="{'active-section': activeSection === 'cash-flow'}" @click="setActiveSection('cash-flow')">
              <div class="section-header">
                <div class="section-icon cash-flow">
                  <i class="fas fa-cash-register"></i>
                </div>
                <h4>现金流量录入</h4>
              </div>
              <div class="input-form">
                <div class="form-row">
                  <div class="form-item">
                    <label>币种</label>
                    <el-select v-model="financeData.dataInput.cashFlowInput.currency" size="small" class="full-width">
                      <el-option
                        v-for="currency in financeData.dataInput.currencies"
                        :key="currency"
                        :label="currency"
                        :value="currency"
                      />
                    </el-select>
                  </div>
                  <div class="form-item">
                    <label>流向类型</label>
                    <el-select v-model="financeData.dataInput.cashFlowInput.flowType" size="small" class="full-width">
                      <el-option label="流入" value="流入" />
                      <el-option label="流出" value="流出" />
                    </el-select>
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-item">
                    <label>金额</label>
                    <el-input-number
                      v-model="financeData.dataInput.cashFlowInput.amount"
                      :precision="2"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                  <div class="form-item">
                    <label>汇率</label>
                    <el-input-number
                      v-model="financeData.dataInput.cashFlowInput.exchangeRate"
                      :precision="4"
                      :step="0.0001"
                      :controls="false"
                      size="small"
                      class="full-width"
                    />
                  </div>
                </div>
                <div class="form-result" v-if="financeData.dataInput.cashFlowInput.amount">
                  <div class="result-item">
                    <span>等值人民币：</span>
                    <span>¥ {{ getCashFlowCnyPreview().toFixed(2) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DataCard>
    </div>
  </AppLayout>
</template>

<style scoped>
.finance-dashboard {
  min-height: 100%;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 16px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.card-icon i {
  font-size: 20px;
  color: #fff;
}

.card-icon.fx-asset {
  background-color: rgba(22, 93, 255, 0.1);
}

.card-icon.fx-asset i {
  color: #165DFF;
}

.card-icon.forward {
  background-color: rgba(54, 203, 203, 0.1);
}

.card-icon.forward i {
  color: #36CBCB;
}

.card-icon.realized {
  background-color: rgba(82, 196, 26, 0.1);
}

.card-icon.realized i {
  color: #52C41A;
}

.card-icon.cash-flow {
  background-color: rgba(250, 173, 20, 0.1);
}

.card-icon.cash-flow i {
  color: #FAAD14;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.currency {
  font-size: 14px;
  margin-right: 2px;
}

.card-change {
  font-size: 14px;
  display: flex;
  align-items: center;
}

.card-change i {
  margin-right: 4px;
}

.increased {
  color: #52C41A;
}

.decreased {
  color: #FF4D4F;
}

/* 更多按钮样式 */
.view-more-btn {
  display: block;
  width: 100%;
  padding: 10px;
  margin-top: 16px;
  background-color: #fff;
  border: 1px solid rgba(22, 93, 255, 0.3);
  color: #165DFF;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s;
}

.view-more-btn:hover {
  background-color: rgba(22, 93, 255, 0.05);
}

.view-details-btn {
  margin-top: 8px;
  background-color: rgba(22, 93, 255, 0.05);
}

.add-contract-btn {
  background-color: #165DFF;
  color: white;
}

.add-contract-btn:hover {
  background-color: #124fd8;
}

/* 外币资产详情对话框样式 */
.asset-dialog-content {
  padding: 0 16px;
}

.asset-summary-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.pie-chart-placeholder {
  height: 150px;
  background-color: #f9f9f9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.pie-chart-placeholder::after {
  content: "币种占比图表";
}

/* 远期合约表单样式 */
.form-row {
  display: flex;
  gap: 16px;
}

.form-item-half {
  width: 50%;
}

.contract-preview {
  margin-top: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px dashed #ddd;
}

.preview-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-label {
  color: #666;
}

.preview-value {
  font-weight: 500;
}

.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

@media (max-width: 768px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }

  .detail-row {
    flex-direction: column;
    gap: 8px;
  }

  .fx-asset-header,
  .contract-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .asset-summary,
  .contract-summary {
    flex-direction: column;
    gap: 16px;
  }

  .asset-card-header,
  .contract-card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .asset-amount,
  .contract-amount {
    text-align: left;
  }

  .rate-comparison {
    flex-direction: column;
    gap: 12px;
  }

  .rate-arrow {
    transform: rotate(90deg);
  }

  .gain-loss-section,
  .valuation-section {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .contract-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .realized-gain-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .summary-item {
    align-items: flex-start;
  }

  .detail-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .cash-flow-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .impact-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

.chart-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.detail-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.asset-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.asset-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

.asset-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.asset-name {
  font-weight: bold;
}

.asset-details {
  border-top: 1px solid #eee;
  padding-top: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 13px;
  color: #666;
}

.detail-value {
  font-weight: 500;
}

.asset-actions-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 16px;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn.secondary {
  background: #f2f3f5;
  color: #4e5969;
  border: 1px solid #e5e6eb;
}

.action-btn.secondary:hover {
  background: #e5e6eb;
  color: #1d2129;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #fff8f0 0%, #fff2e8 100%);
  border-radius: 8px;
  border-left: 4px solid #FF7D00;
}

.contract-summary {
  display: flex;
  gap: 32px;
}

.contract-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contract-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.contract-card {
  background: #fff;
  border: 1px solid #e5e6eb;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contract-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 125, 0, 0.15);
}

.contract-card.contract-gain {
  border-left: 4px solid #00B42A;
  background: linear-gradient(135deg, #fff 0%, #f6ffed 100%);
}

.contract-card.contract-loss {
  border-left: 4px solid #F53F3F;
  background: linear-gradient(135deg, #fff 0%, #fff2f0 100%);
}

.contract-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.contract-title h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 8px 0;
}

.contract-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.contract-amount {
  text-align: right;
}

.contract-card-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contract-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.detail-label {
  font-size: 12px;
  color: #86909c;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
}

.valuation-section {
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
}

.valuation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.valuation-label {
  font-size: 14px;
  color: #86909c;
  font-weight: 500;
}

.valuation-value {
  font-size: 16px;
  font-weight: 600;
}

@media (max-width: 1200px) {
  .summary-cards {
    grid-template-columns: 1fr 1fr;
  }

  .chart-section,
  .detail-section {
    grid-template-columns: 1fr;
  }

  .input-grid {
    grid-template-columns: repeat(1, 1fr);
  }

  .asset-cards-grid,
  .contract-cards-grid {
    grid-template-columns: 1fr;
  }
}
</style>

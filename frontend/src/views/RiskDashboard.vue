<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import AppLayout from '../components/layout/AppLayout.vue'
import DataCard from '../components/common/DataCard.vue'
import LineChart from '../components/charts/LineChart.vue'
import PieChart from '../components/charts/PieChart.vue'
import { riskAPI } from '../utils/api'
import { formatCurrency, formatPercent, formatLargeNumber } from '../utils/format'

// 加载状态
const loading = ref(true)

// 风险仪表盘数据
const riskData = reactive({
  // 概览数据（4个卡片）
  overview: {
    netUsdExposure: 0,
    netUsdExposureChange: 0,
    netUsdExposureDecreased: false,
    volatility: 0,
    volatilityChange: 0,
    volatilityIncreased: false,
    var: 0,
    varChange: 0,
    varIncreased: false,
    riskLevel: '',
    riskScore: 0
  },
  // 汇率变化分析
  exchangeRateAnalysis: {
    dates: [],
    rates: {},
    keyIndicators: {},
    correlations: []
  },
  // 风险量化模型
  riskModels: [],
  // 敞口分析
  exposureAnalysis: {
    currencyExposures: [],
    timeDistribution: []
  },
  // 结汇建议
  recommendations: []
})

// 汇率变化分析图表数据
const exchangeRateChartData = ref({
  labels: [],
  datasets: []
})

// 风险雷达图表数据
const riskRadarChartData = ref({
  labels: ['汇率风险', '流动性风险', '信用风险', '操作风险', '市场风险', '政策风险'],
  datasets: [
    {
      label: '风险评估',
      data: [],
      backgroundColor: 'rgba(22, 93, 255, 0.2)',
      borderColor: 'rgba(22, 93, 255, 1)',
      borderWidth: 2
    }
  ]
})

// 资产配置饼图数据
const allocationChartData = ref({
  labels: ['美元资产', '人民币资产'],
  datasets: [
    {
      data: [40, 60],
      backgroundColor: ['#165DFF', '#0FC6C2'],
      borderWidth: 0
    }
  ]
})

// 时间周期选择
const period = ref('3m')
const periods = [
  { label: '最近3个月', value: '3m' },
  { label: '最近6个月', value: '6m' },
  { label: '最近12个月', value: '12m' }
]

// 获取风险仪表盘数据
const fetchRiskData = async () => {
  loading.value = true
  try {
    const data = await riskAPI.getRiskDashboard()
    Object.assign(riskData, data)

    // 更新图表数据
    updateExchangeRateChart()
    updateRiskRadarChart()

    loading.value = false
  } catch (error) {
    console.error('获取风险仪表盘数据失败:', error)
    loading.value = false
  }
}

// 获取汇率变化分析数据
const fetchExchangeRateAnalysis = async (periodValue) => {
  try {
    const data = await riskAPI.getExchangeRateAnalysis({ period: periodValue })
    riskData.exchangeRateAnalysis = data
    updateExchangeRateChart()
  } catch (error) {
    console.error('获取汇率变化分析数据失败:', error)
  }
}

// 更新汇率变化图表
const updateExchangeRateChart = () => {
  const analysis = riskData.exchangeRateAnalysis
  if (!analysis.dates || !analysis.rates) return

  const datasets = []

  // USD/CNY 即期汇率
  if (analysis.rates['USD/CNY']) {
    datasets.push({
      label: 'USD/CNY 即期汇率',
      data: analysis.rates['USD/CNY'],
      borderColor: '#165DFF',
      backgroundColor: 'rgba(22, 93, 255, 0.1)',
      borderWidth: 2,
      fill: true,
      tension: 0.3
    })
  }

  // 20日均线
  if (analysis.rates['MA20']) {
    datasets.push({
      label: '20日均线',
      data: analysis.rates['MA20'],
      borderColor: '#FF7D00',
      borderWidth: 2,
      borderDash: [5, 5],
      fill: false,
      tension: 0.3
    })
  }

  exchangeRateChartData.value = {
    labels: analysis.dates,
    datasets
  }
}

// 更新风险雷达图表
const updateRiskRadarChart = () => {
  // 模拟风险评估数据
  const riskScores = [65, 25, 40, 75, 55, 30] // 对应6个风险维度

  riskRadarChartData.value = {
    labels: ['汇率风险', '流动性风险', '信用风险', '操作风险', '市场风险', '政策风险'],
    datasets: [
      {
        label: '风险评估',
        data: riskScores,
        backgroundColor: 'rgba(22, 93, 255, 0.2)',
        borderColor: 'rgba(22, 93, 255, 1)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(22, 93, 255, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(22, 93, 255, 1)'
      }
    ]
  }
}

// 期间切换
const handlePeriodChange = (value) => {
  period.value = value
  fetchExchangeRateAnalysis(value)
}

// 风险等级评估
const getRiskLevel = (score) => {
  if (score <= 20) return { text: '低风险', class: 'low-risk', color: '#00B42A' }
  if (score <= 40) return { text: '中低风险', class: 'medium-low-risk', color: '#FAAD14' }
  if (score <= 60) return { text: '中等风险', class: 'medium-risk', color: '#FF7D00' }
  if (score <= 80) return { text: '中高风险', class: 'medium-high-risk', color: '#F53F3F' }
  return { text: '高风险', class: 'high-risk', color: '#CC0000' }
}

// 格式化货币
const formatCurrencyValue = (value, currency = '¥') => {
  if (!value) return `${currency}0`
  return `${currency}${new Intl.NumberFormat('zh-CN').format(value)}`
}

// 格式化百分比
const formatPercentValue = (value) => {
  if (!value) return '0%'
  return `${value}%`
}

// 重新计算风险模型
const recalculateRiskModels = async () => {
  try {
    loading.value = true
    await riskAPI.recalculateRiskModels()
    await fetchRiskData()
  } catch (error) {
    console.error('重新计算风险模型失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchRiskData()
})
</script>

<template>
  <AppLayout>
    <div class="risk-dashboard">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-title">
            <i class="fas fa-chart-line"></i>
            <span>外汇风险管理驾驶舱</span>
          </div>
        </div>
      </div>

      <!-- 概览卡片区域 -->
      <section class="overview-section">
        <div class="overview-cards">
          <!-- 净美元敞口 -->
          <div class="overview-card">
            <div class="card-header">
              <h3>净美元敞口</h3>
              <span
                class="change-indicator"
                :class="{ 'positive': !riskData.overview.netUsdExposureDecreased, 'negative': riskData.overview.netUsdExposureDecreased }"
              >
                <i :class="riskData.overview.netUsdExposureDecreased ? 'fas fa-arrow-down' : 'fas fa-arrow-up'"></i>
                {{ formatPercentValue(Math.abs(riskData.overview.netUsdExposureChange)) }}
              </span>
            </div>
            <div class="card-content">
              <div class="main-value">{{ formatCurrencyValue(riskData.overview.netUsdExposure, '$') }}</div>
              <div class="sub-text">较上月{{ riskData.overview.netUsdExposureDecreased ? '减少' : '增加' }} ${{ Math.abs(riskData.overview.netUsdExposureChange) }}</div>
            </div>
            <div class="card-icon">
              <i class="fas fa-dollar-sign"></i>
            </div>
          </div>

          <!-- 汇率波动率 -->
          <div class="overview-card">
            <div class="card-header">
              <h3>汇率波动率</h3>
              <span
                class="change-indicator"
                :class="{ 'positive': riskData.overview.volatilityIncreased, 'negative': !riskData.overview.volatilityIncreased }"
              >
                <i :class="riskData.overview.volatilityIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ formatPercentValue(Math.abs(riskData.overview.volatilityChange)) }}
              </span>
            </div>
            <div class="card-content">
              <div class="main-value">{{ formatPercentValue(riskData.overview.volatility) }}</div>
              <div class="sub-text">较上月{{ riskData.overview.volatilityIncreased ? '增加' : '减少' }} {{ Math.abs(riskData.overview.volatilityChange) }}%</div>
            </div>
            <div class="card-icon">
              <i class="fas fa-chart-area"></i>
            </div>
          </div>

          <!-- 95% VaR -->
          <div class="overview-card">
            <div class="card-header">
              <h3>95% VaR</h3>
              <span
                class="change-indicator"
                :class="{ 'positive': riskData.overview.varIncreased, 'negative': !riskData.overview.varIncreased }"
              >
                <i :class="riskData.overview.varIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                {{ formatPercentValue(Math.abs(riskData.overview.varChange)) }}
              </span>
            </div>
            <div class="card-content">
              <div class="main-value">{{ formatCurrencyValue(riskData.overview.var) }}</div>
              <div class="sub-text">较上月{{ riskData.overview.varIncreased ? '增加' : '减少' }} ¥{{ Math.abs(riskData.overview.varChange) }}</div>
            </div>
            <div class="card-icon">
              <i class="fas fa-shield-exclamation"></i>
            </div>
          </div>

          <!-- 风险等级 -->
          <div class="overview-card">
            <div class="card-header">
              <h3>风险等级</h3>
              <span class="risk-level-badge" :style="{ color: getRiskLevel(riskData.overview.riskScore).color }">
                {{ getRiskLevel(riskData.overview.riskScore).text }}
              </span>
            </div>
            <div class="card-content">
              <div class="risk-progress">
                <div class="progress-bar">
                  <div
                    class="progress"
                    :style="{
                      width: `${riskData.overview.riskScore}%`,
                      backgroundColor: getRiskLevel(riskData.overview.riskScore).color
                    }"
                  ></div>
                </div>
                <div class="progress-text">风险指数: {{ riskData.overview.riskScore }}/100</div>
              </div>
            </div>
            <div class="card-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
          </div>
        </div>
      </section>

      <!-- 汇率变化分析部分 -->
      <section class="exchange-rate-section">
        <DataCard title="汇率变化分析" :loading="loading">
          <div class="chart-header">
            <el-select v-model="period" @change="handlePeriodChange" size="small" style="width: 120px">
              <el-option
                v-for="item in periods"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <div class="exchange-rate-content">
            <div class="chart-container">
              <LineChart :chart-data="exchangeRateChartData" :height="300" />
            </div>

            <div class="indicators-panel">
              <h3>关键汇率指标</h3>
              <div class="indicators-list">
                <div
                  v-for="(value, key) in riskData.exchangeRateAnalysis.keyIndicators"
                  :key="key"
                  class="indicator-item"
                >
                  <span class="indicator-label">{{ key }}</span>
                  <span class="indicator-value">{{ value }}</span>
                </div>
              </div>

              <div class="correlations-section">
                <h3>相关性分析</h3>
                <div
                  v-for="correlation in riskData.exchangeRateAnalysis.correlations"
                  :key="correlation.pair"
                  class="correlation-item"
                >
                  <div class="correlation-header">
                    <span class="correlation-pair">{{ correlation.pair }}</span>
                    <span class="correlation-value">{{ correlation.value }}</span>
                  </div>
                  <div class="correlation-bar">
                    <div
                      class="correlation-progress"
                      :style="{ width: `${correlation.value * 100}%` }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DataCard>
      </section>

      <!-- 风险量化模型部分 -->
      <section class="risk-models-section">
        <DataCard title="风险量化模型" :loading="loading">
          <div class="models-header">
            <el-button type="primary" @click="recalculateRiskModels" :loading="loading">
              <i class="fas fa-refresh"></i>
              重新计算
            </el-button>
          </div>

          <div class="risk-models-grid">
            <div
              v-for="model in riskData.riskModels"
              :key="model.name"
              class="risk-model-card"
            >
              <div class="model-header">
                <h3>{{ model.name }}</h3>
                <span class="model-badge">{{ model.impact }}</span>
              </div>

              <div class="model-content">
                <div class="model-description">{{ model.description }}</div>

                <div class="model-metrics">
                  <div class="metric-item">
                    <span class="metric-label">当前值</span>
                    <span class="metric-value">{{ formatCurrencyValue(model.value) }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">趋势</span>
                    <span
                      class="metric-trend"
                      :class="{
                        'trend-up': model.trend === '上升',
                        'trend-down': model.trend === '下降',
                        'trend-stable': model.trend === '稳定'
                      }"
                    >
                      <i :class="{
                        'fas fa-arrow-up': model.trend === '上升',
                        'fas fa-arrow-down': model.trend === '下降',
                        'fas fa-minus': model.trend === '稳定'
                      }"></i>
                      {{ model.trend }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DataCard>
      </section>

      <!-- 风险仪表盘部分 -->
      <section class="risk-dashboard-section">
        <DataCard title="风险仪表盘" :loading="loading">
          <div class="dashboard-content">
            <div class="radar-chart-container">
              <h3>综合风险评估</h3>
              <!-- 这里需要雷达图组件，暂时用占位符 -->
              <div class="radar-chart-placeholder">
                <div class="chart-info">雷达图显示综合风险评估</div>
              </div>
            </div>

            <div class="risk-indicators">
              <div class="risk-gauge">
                <h3>风险等级</h3>
                <div class="gauge-container">
                  <div class="gauge-display">
                    <span class="gauge-value">{{ getRiskLevel(riskData.overview.riskScore).text }}</span>
                    <span class="gauge-label">风险</span>
                  </div>
                </div>
              </div>

              <div class="risk-metrics">
                <h3>风险指标</h3>
                <div class="metrics-list">
                  <div class="metric-bar">
                    <div class="metric-info">
                      <span>汇率风险</span>
                      <span>65%</span>
                    </div>
                    <div class="progress-bar">
                      <div class="progress" style="width: 65%; background-color: #FF7D00;"></div>
                    </div>
                  </div>

                  <div class="metric-bar">
                    <div class="metric-info">
                      <span>流动性风险</span>
                      <span>25%</span>
                    </div>
                    <div class="progress-bar">
                      <div class="progress" style="width: 25%; background-color: #00B42A;"></div>
                    </div>
                  </div>

                  <div class="metric-bar">
                    <div class="metric-info">
                      <span>信用风险</span>
                      <span>40%</span>
                    </div>
                    <div class="progress-bar">
                      <div class="progress" style="width: 40%; background-color: #86909C;"></div>
                    </div>
                  </div>

                  <div class="metric-bar">
                    <div class="metric-info">
                      <span>操作风险</span>
                      <span>75%</span>
                    </div>
                    <div class="progress-bar">
                      <div class="progress" style="width: 75%; background-color: #F53F3F;"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DataCard>
      </section>

      <!-- 结汇建议部分 -->
      <section class="recommendation-section">
        <DataCard title="结汇建议" :loading="loading">
          <div class="recommendation-content">
            <div class="allocation-section">
              <h3>最优结汇比例建议</h3>
              <div class="allocation-display">
                <div class="allocation-chart">
                  <PieChart :chart-data="allocationChartData" :height="250" />
                </div>

                <div class="allocation-details">
                  <div class="allocation-item">
                    <div class="allocation-header">
                      <div class="color-indicator" style="background-color: #165DFF;"></div>
                      <span>美元资产</span>
                    </div>
                    <div class="allocation-value">
                      <span class="percentage">40%</span>
                      <span class="amount">约 $103,200</span>
                    </div>
                  </div>

                  <div class="allocation-item">
                    <div class="allocation-header">
                      <div class="color-indicator" style="background-color: #0FC6C2;"></div>
                      <span>人民币资产</span>
                    </div>
                    <div class="allocation-value">
                      <span class="percentage">60%</span>
                      <span class="amount">约 ¥699,600</span>
                    </div>
                  </div>

                  <div class="allocation-note">
                    <i class="fas fa-info-circle"></i>
                    <span>基于当前市场条件和风险偏好，建议维持40:60的美元/人民币资产配置比例，以平衡风险与收益。</span>
                  </div>
                </div>
              </div>

              <div class="strategy-section">
                <h3>风险管理策略</h3>
                <div class="strategy-list">
                  <div class="strategy-item">
                    <div class="strategy-icon success">
                      <i class="fas fa-check"></i>
                    </div>
                    <div class="strategy-content">
                      <h4>对冲策略</h4>
                      <p>考虑使用外汇远期合约对冲约50%的预期美元收入，锁定当前有利汇率水平，降低汇率波动风险。</p>
                    </div>
                  </div>

                  <div class="strategy-item">
                    <div class="strategy-icon success">
                      <i class="fas fa-check"></i>
                    </div>
                    <div class="strategy-content">
                      <h4>资产配置</h4>
                      <p>按照马科维茨有效前沿模型建议，维持40%美元资产和60%人民币资产的配置比例，定期（每季度）进行再平衡。</p>
                    </div>
                  </div>

                  <div class="strategy-item">
                    <div class="strategy-icon warning">
                      <i class="fas fa-exclamation"></i>
                    </div>
                    <div class="strategy-content">
                      <h4>风险监控</h4>
                      <p>密切关注美元指数走势及中美货币政策变化，每周更新风险模型，当VaR超过阈值（¥35,000）时及时调整策略。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="recommendation-sidebar">
              <div class="forecast-section">
                <h3>汇率预测</h3>
                <div class="forecast-list">
                  <div class="forecast-item">
                    <span class="forecast-period">1个月预测</span>
                    <span class="forecast-range danger">6.86-6.90</span>
                  </div>
                  <div class="forecast-item">
                    <span class="forecast-period">3个月预测</span>
                    <span class="forecast-range danger">6.92-6.98</span>
                  </div>
                  <div class="forecast-item">
                    <span class="forecast-period">6个月预测</span>
                    <span class="forecast-range warning">6.95-7.05</span>
                  </div>
                  <div class="forecast-item">
                    <span class="forecast-period">12个月预测</span>
                    <span class="forecast-range warning">7.00-7.15</span>
                  </div>
                </div>

                <div class="forecast-note">
                  <i class="fas fa-info-circle"></i>
                  <span>预测基于历史数据分析、经济模型及市场趋势，仅供参考，实际汇率可能受多种因素影响。</span>
                </div>
              </div>

              <div class="actions-section">
                <h3>推荐操作</h3>
                <div class="action-list">
                  <div class="action-item primary">
                    <div class="action-icon">
                      <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="action-content">
                      <h4>短期操作</h4>
                      <p>考虑在未来1-2周内进行部分结汇，锁定当前相对较高的汇率水平。</p>
                    </div>
                  </div>

                  <div class="action-item warning">
                    <div class="action-icon">
                      <i class="fas fa-clock"></i>
                    </div>
                    <div class="action-content">
                      <h4>中期策略</h4>
                      <p>建立滚动对冲机制，每月对冲25%的预期美元收入，分散汇率波动风险。</p>
                    </div>
                  </div>

                  <div class="action-item info">
                    <div class="action-icon">
                      <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="action-content">
                      <h4>长期规划</h4>
                      <p>考虑增加人民币资产配置比例至60%，降低美元敞口风险，实现资产多元化。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DataCard>
      </section>
    </div>
  </AppLayout>
</template>

<style scoped>
.risk-dashboard {
  min-height: 100%;
  background-color: #f5f5f5;
}

/* 页面标题 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  background: linear-gradient(135deg, #165DFF 0%, #0FC6C2 100%);
  padding: 20px 24px;
  border-radius: 12px;
  color: white;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
}

.header-title i {
  margin-right: 12px;
  font-size: 28px;
}

/* 概览卡片 */
.overview-section {
  margin-bottom: 24px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.overview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(22, 93, 255, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h3 {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.change-indicator {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.change-indicator i {
  margin-right: 4px;
}

.change-indicator.positive {
  color: #00B42A;
}

.change-indicator.negative {
  color: #F53F3F;
}

.card-content {
  margin-bottom: 16px;
}

.main-value {
  font-size: 32px;
  font-weight: bold;
  color: #1d2129;
  margin-bottom: 8px;
}

.sub-text {
  font-size: 14px;
  color: #86909c;
}

.card-icon {
  position: absolute;
  bottom: 24px;
  right: 24px;
  font-size: 32px;
  color: #165DFF;
  opacity: 0.15;
  z-index: 1;
}

.risk-level-badge {
  font-size: 14px;
  font-weight: 500;
}

.risk-progress {
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #f2f3f5;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #86909c;
}

/* 汇率变化分析 */
.exchange-rate-section {
  margin-bottom: 24px;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.exchange-rate-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.chart-container {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

.indicators-panel {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.indicators-panel h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1d2129;
}

.indicators-list {
  margin-bottom: 24px;
}

.indicator-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e6eb;
}

.indicator-item:last-child {
  border-bottom: none;
}

.indicator-label {
  color: #86909c;
  font-size: 14px;
}

.indicator-value {
  font-weight: 600;
  color: #1d2129;
}

.correlations-section {
  padding-top: 24px;
  border-top: 1px solid #e5e6eb;
}

.correlation-item {
  margin-bottom: 16px;
}

.correlation-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.correlation-pair {
  color: #86909c;
  font-size: 14px;
}

.correlation-value {
  font-weight: 600;
  color: #1d2129;
}

.correlation-bar {
  width: 100%;
  height: 6px;
  background-color: #e5e6eb;
  border-radius: 3px;
  overflow: hidden;
}

.correlation-progress {
  height: 100%;
  background: linear-gradient(90deg, #165DFF 0%, #0FC6C2 100%);
  border-radius: 3px;
}

/* 风险量化模型 */
.risk-models-section {
  margin-bottom: 24px;
}

.models-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.risk-models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.risk-model-card {
  background: #f9f9f9;
  border-radius: 12px;
  padding: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.risk-model-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(22, 93, 255, 0.1);
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.model-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  margin: 0;
}

.model-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  background-color: rgba(22, 93, 255, 0.1);
  color: #165DFF;
}

.model-content {
  margin-bottom: 16px;
}

.model-description {
  color: #86909c;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.model-metrics {
  display: flex;
  justify-content: space-between;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 13px;
  color: #86909c;
}

.metric-value {
  font-weight: 600;
  color: #1d2129;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.trend-up {
  color: #F53F3F;
}

.trend-down {
  color: #00B42A;
}

.trend-stable {
  color: #86909c;
}

/* 风险仪表盘 */
.risk-dashboard-section {
  margin-bottom: 24px;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.radar-chart-container {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.radar-chart-container h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1d2129;
}

.radar-chart-placeholder {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e5e6eb;
  border-radius: 8px;
  color: #86909c;
}

.risk-indicators {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.risk-gauge {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.risk-gauge h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1d2129;
}

.gauge-container {
  display: flex;
  justify-content: center;
}

.gauge-display {
  text-align: center;
}

.gauge-value {
  font-size: 32px;
  font-weight: bold;
  color: #FF7D00;
  display: block;
}

.gauge-label {
  font-size: 14px;
  color: #86909c;
}

.risk-metrics {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.risk-metrics h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1d2129;
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-bar {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.metric-info span:first-child {
  color: #86909c;
}

.metric-info span:last-child {
  font-weight: 600;
  color: #1d2129;
}

/* 结汇建议 */
.recommendation-section {
  margin-bottom: 24px;
}

.recommendation-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.allocation-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1d2129;
}

.allocation-display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.allocation-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
}

.allocation-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.allocation-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.allocation-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.percentage {
  font-size: 24px;
  font-weight: bold;
  color: #1d2129;
}

.amount {
  font-size: 14px;
  color: #86909c;
}

.allocation-note {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 16px;
  background: rgba(22, 93, 255, 0.05);
  border-radius: 8px;
  border-left: 4px solid #165DFF;
  font-size: 14px;
  color: #86909c;
  line-height: 1.5;
}

.strategy-section {
  padding-top: 24px;
  border-top: 1px solid #e5e6eb;
}

.strategy-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.strategy-item {
  display: flex;
  gap: 12px;
}

.strategy-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
}

.strategy-icon.success {
  background-color: rgba(0, 180, 42, 0.2);
  color: #00B42A;
}

.strategy-icon.warning {
  background-color: rgba(255, 125, 0, 0.2);
  color: #FF7D00;
}

.strategy-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 4px 0;
}

.strategy-content p {
  font-size: 14px;
  color: #86909c;
  line-height: 1.5;
  margin: 0;
}

.recommendation-sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.forecast-section,
.actions-section {
  background: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.forecast-section h3,
.actions-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1d2129;
}

.forecast-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.forecast-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forecast-period {
  color: #86909c;
  font-size: 14px;
}

.forecast-range {
  font-weight: 600;
  font-size: 14px;
}

.forecast-range.danger {
  color: #F53F3F;
}

.forecast-range.warning {
  color: #FF7D00;
}

.forecast-note {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 12px;
  color: #86909c;
  line-height: 1.4;
}

.action-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
}

.action-item.primary {
  background-color: rgba(22, 93, 255, 0.1);
}

.action-item.warning {
  background-color: rgba(255, 125, 0, 0.1);
}

.action-item.info {
  background-color: rgba(134, 144, 156, 0.1);
}

.action-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-item.primary .action-icon {
  background-color: rgba(22, 93, 255, 0.2);
  color: #165DFF;
}

.action-item.warning .action-icon {
  background-color: rgba(255, 125, 0, 0.2);
  color: #FF7D00;
}

.action-item.info .action-icon {
  background-color: rgba(134, 144, 156, 0.2);
  color: #86909c;
}

.action-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 4px 0;
}

.action-content p {
  font-size: 14px;
  color: #86909c;
  line-height: 1.5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .exchange-rate-content,
  .dashboard-content,
  .recommendation-content {
    grid-template-columns: 1fr;
  }

  .allocation-display {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }

  .risk-models-grid {
    grid-template-columns: 1fr;
  }

  .model-metrics {
    flex-direction: column;
    gap: 12px;
  }

  .strategy-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-item {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import AppLayout from '../components/layout/AppLayout.vue'
import DataCard from '../components/common/DataCard.vue'
import LineChart from '../components/charts/LineChart.vue'
import PieChart from '../components/charts/PieChart.vue'
import { riskAPI } from '../utils/api'
import { formatCurrency, formatPercent, formatLargeNumber } from '../utils/format'

// 加载状态
const loading = ref(true)

// 风险仪表盘数据
const riskData = reactive({
  // 概览数据（4个卡片）
  overview: {
    netUsdExposure: 0,
    netUsdExposureChange: 0,
    netUsdExposureDecreased: false,
    volatility: 0,
    volatilityChange: 0,
    volatilityIncreased: false,
    var: 0,
    varChange: 0,
    varIncreased: false,
    riskLevel: '',
    riskScore: 0
  },
  // 汇率变化分析
  exchangeRateAnalysis: {
    dates: [],
    rates: {},
    keyIndicators: {},
    correlations: []
  },
  // 风险量化模型
  riskModels: [],
  // 敞口分析
  exposureAnalysis: {
    currencyExposures: [],
    timeDistribution: []
  },
  // 结汇建议
  recommendations: []
})

// 敞口分布图表数据
const exposureDistributionChartData = ref({
  labels: [],
  datasets: [
    {
      backgroundColor: [],
      data: []
    }
  ]
})

// 历史敞口图表数据
const historicalExposureChartData = ref({
  labels: [],
  datasets: [
    {
      label: '总敞口',
      borderColor: '#F53F3F',
      backgroundColor: 'rgba(245, 63, 63, 0.1)',
      data: [],
      fill: true
    },
    {
      label: '已对冲',
      borderColor: '#00B42A',
      backgroundColor: 'transparent',
      data: [],
      borderDash: [5, 5]
    }
  ]
})

// 风险评估图表数据
const riskAssessmentChartData = ref({
  labels: ['市场风险', '流动性风险', '信用风险', '操作风险'],
  datasets: [
    {
      backgroundColor: [
        'rgba(245, 63, 63, 0.7)',
        'rgba(250, 173, 20, 0.7)',
        'rgba(22, 93, 255, 0.7)',
        'rgba(134, 144, 156, 0.7)'
      ],
      data: [0, 0, 0, 0]
    }
  ]
})

// 时间周期选择
const period = ref('month')
const periods = [
  { label: '近30天', value: 'month' },
  { label: '近90天', value: 'quarter' },
  { label: '年度', value: 'year' }
]

// 获取风险仪表盘数据
const fetchRiskData = async () => {
  loading.value = true
  try {
    const data = await riskAPI.getRiskDashboard()
    Object.assign(riskData, data)

    // 更新图表数据
    updateExposureDistributionChart()
    updateHistoricalExposureChart()
    updateRiskAssessmentChart()

    loading.value = false
  } catch (error) {
    console.error('获取风险仪表盘数据失败:', error)
    loading.value = false
  }
}

// 获取历史敞口数据
const fetchHistoricalExposureData = async (periodValue) => {
  try {
    const data = await riskAPI.getHistoricalExposure({ period: periodValue })
    riskData.historicalExposure = data
    updateHistoricalExposureChart()
  } catch (error) {
    console.error('获取历史敞口数据失败:', error)
  }
}

// 更新敞口分布图表
const updateExposureDistributionChart = () => {
  const labels = riskData.exposureDistribution.map(item => item.currency)
  const data = riskData.exposureDistribution.map(item => item.amount)
  const colors = riskData.exposureDistribution.map(item => item.color)

  exposureDistributionChartData.value = {
    labels,
    datasets: [
      {
        backgroundColor: colors,
        data
      }
    ]
  }
}

// 更新历史敞口图表
const updateHistoricalExposureChart = () => {
  historicalExposureChartData.value = {
    labels: riskData.historicalExposure.dates,
    datasets: [
      {
        label: '总敞口',
        borderColor: '#F53F3F',
        backgroundColor: 'rgba(245, 63, 63, 0.1)',
        data: riskData.historicalExposure.values,
        fill: true
      },
      {
        label: '已对冲',
        borderColor: '#00B42A',
        backgroundColor: 'transparent',
        data: riskData.historicalExposure.hedgedValues,
        borderDash: [5, 5]
      }
    ]
  }
}

// 更新风险评估图表
const updateRiskAssessmentChart = () => {
  riskAssessmentChartData.value = {
    labels: ['市场风险', '流动性风险', '信用风险', '操作风险'],
    datasets: [
      {
        backgroundColor: [
          'rgba(245, 63, 63, 0.7)',
          'rgba(250, 173, 20, 0.7)',
          'rgba(22, 93, 255, 0.7)',
          'rgba(134, 144, 156, 0.7)'
        ],
        data: [
          riskData.riskAssessment.marketRisk,
          riskData.riskAssessment.liquidityRisk,
          riskData.riskAssessment.creditRisk,
          riskData.riskAssessment.operationalRisk
        ]
      }
    ]
  }
}

// 期间切换
const handlePeriodChange = (value) => {
  period.value = value
  fetchHistoricalExposureData(value)
}

// 对冲比例计算
const hedgeRatio = computed(() => {
  return (riskData.summary.totalHedged / riskData.summary.totalExposure * 100).toFixed(1)
})

// 风险等级评估
const getRiskLevel = (value) => {
  if (value <= 20) return { text: '低风险', class: 'low-risk' }
  if (value <= 40) return { text: '中低风险', class: 'medium-low-risk' }
  if (value <= 60) return { text: '中等风险', class: 'medium-risk' }
  if (value <= 80) return { text: '中高风险', class: 'medium-high-risk' }
  return { text: '高风险', class: 'high-risk' }
}

// 压力测试结果等级
const getStressTestLevel = (item) => {
  const impactRatio = Math.abs(item.impact / riskData.summary.totalExposure * 100)

  if (impactRatio <= 5) return { text: '影响轻微', class: 'impact-low' }
  if (impactRatio <= 10) return { text: '影响一般', class: 'impact-medium' }
  if (impactRatio <= 20) return { text: '影响较大', class: 'impact-high' }
  return { text: '影响严重', class: 'impact-severe' }
}

// 页面加载时获取数据
onMounted(() => {
  fetchRiskData()
})
</script>

<template>
  <AppLayout>
    <div class="risk-dashboard">
      <!-- 风险概览区域 -->
      <div class="risk-summary">
        <DataCard title="风险敞口概览" :loading="loading">
          <div class="summary-content">
            <div class="summary-header">
              <div class="exposure-value">
                <div class="value-label">外汇风险敞口总额</div>
                <div class="value-number">
                  <span class="currency">¥</span>
                  {{ new Intl.NumberFormat('zh-CN').format(riskData.summary.totalExposure) }}
                </div>
                <div
                  class="value-change"
                  :class="{ 'increased': riskData.summary.exposureIncreased, 'decreased': !riskData.summary.exposureIncreased }"
                >
                  <i :class="riskData.summary.exposureIncreased ? 'fas fa-arrow-up' : 'fas fa-arrow-down'"></i>
                  {{ riskData.summary.exposureChange }}
                </div>
              </div>

              <div class="exposure-metrics">
                <div class="metric">
                  <div class="metric-label">已对冲金额</div>
                  <div class="metric-value">
                    <span class="currency">¥</span>
                    {{ new Intl.NumberFormat('zh-CN').format(riskData.summary.totalHedged) }}
                  </div>
                  <div class="metric-ratio">
                    <div class="progress-bar">
                      <div class="progress" :style="{ width: `${hedgeRatio}%` }"></div>
                    </div>
                    <div class="ratio-text">{{ hedgeRatio }}%</div>
                  </div>
                </div>

                <div class="metric">
                  <div class="metric-label">未对冲敞口比例</div>
                  <div class="metric-value">{{ riskData.summary.unhedgedRatio }}%</div>
                  <div class="metric-comment">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>建议比例不超过30%</span>
                  </div>
                </div>

                <div class="metric">
                  <div class="metric-label">风险价值(VaR, 95%)</div>
                  <div class="metric-value">
                    <span class="currency">¥</span>
                    {{ new Intl.NumberFormat('zh-CN').format(riskData.summary.varValue) }}
                  </div>
                  <div class="metric-comment">
                    <i class="fas fa-info-circle"></i>
                    <span>可能面临的最大损失</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="chart-section">
              <div class="chart-col">
                <h4 class="chart-title">敞口分布</h4>
                <PieChart :chart-data="exposureDistributionChartData" :height="200" />
              </div>

              <div class="chart-col">
                <h4 class="chart-title">风险评估</h4>
                <PieChart :chart-data="riskAssessmentChartData" :height="200" />
              </div>
            </div>
          </div>
        </DataCard>
      </div>

      <!-- 历史敞口趋势 -->
      <DataCard title="敞口趋势分析" :loading="loading">
        <div class="chart-header">
          <el-radio-group v-model="period" @change="handlePeriodChange" size="small">
            <el-radio-button
              v-for="item in periods"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <LineChart :chart-data="historicalExposureChartData" :height="300" />
      </DataCard>

      <!-- 风险详情区域 -->
      <div class="risk-details">
        <!-- 对冲建议 -->
        <DataCard title="风险对冲建议" :loading="loading">
          <div class="hedging-list">
            <div
              v-for="(item, index) in riskData.hedgingRecommendations"
              :key="index"
              class="hedging-item"
            >
              <div class="hedging-header">
                <div class="hedging-title">
                  <i class="fas fa-shield-alt"></i>
                  <span>{{ item.title }}</span>
                </div>

                <div
                  class="hedging-priority"
                  :class="{
                    'high-priority': item.priority === 'high',
                    'medium-priority': item.priority === 'medium',
                    'low-priority': item.priority === 'low'
                  }"
                >
                  {{ item.priority === 'high' ? '高优先级' : item.priority === 'medium' ? '中优先级' : '低优先级' }}
                </div>
              </div>

              <div class="hedging-description">{{ item.description }}</div>

              <div class="hedging-metrics">
                <div class="hedging-metric">
                  <span class="metric-label">预计成本:</span>
                  <span class="metric-value">¥{{ new Intl.NumberFormat('zh-CN').format(item.estimatedCost) }}</span>
                </div>
                <div class="hedging-metric">
                  <span class="metric-label">风险减少:</span>
                  <span class="metric-value">¥{{ new Intl.NumberFormat('zh-CN').format(item.riskReduction) }}</span>
                </div>
                <div class="hedging-metric">
                  <span class="metric-label">ROI:</span>
                  <span
                    class="metric-value"
                    :class="{ 'positive': item.roi > 0, 'negative': item.roi < 0 }"
                  >
                    {{ item.roi > 0 ? '+' : '' }}{{ item.roi }}%
                  </span>
                </div>
              </div>

              <div class="hedging-actions">
                <el-button size="small" type="primary">执行对冲</el-button>
                <el-button size="small">查看详情</el-button>
              </div>
            </div>
          </div>
        </DataCard>

        <!-- 压力测试 -->
        <DataCard title="风险压力测试" :loading="loading">
          <el-table :data="riskData.stressTests" style="width: 100%">
            <el-table-column prop="scenario" label="压力情景" width="200" />
            <el-table-column prop="probability" label="发生概率" width="100">
              <template #default="scope">
                {{ scope.row.probability }}%
              </template>
            </el-table-column>
            <el-table-column prop="impact" label="预计影响" width="150">
              <template #default="scope">
                <span :class="scope.row.impact >= 0 ? 'positive' : 'negative'">
                  {{ scope.row.impact >= 0 ? '+' : '' }}¥{{ new Intl.NumberFormat('zh-CN').format(scope.row.impact) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="impactLevel" label="影响程度">
              <template #default="scope">
                <div
                  class="impact-level"
                  :class="getStressTestLevel(scope.row).class"
                >
                  {{ getStressTestLevel(scope.row).text }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="action" label="应对措施" width="120">
              <template #default>
                <el-button size="small" type="text">查看措施</el-button>
              </template>
            </el-table-column>
          </el-table>
        </DataCard>
      </div>
    </div>
  </AppLayout>
</template>

<style scoped>
.risk-dashboard {
  min-height: 100%;
}

.risk-summary {
  margin-bottom: 24px;
}

.summary-content {
  padding: 16px 0;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}

.exposure-value {
  padding-right: 24px;
  border-right: 1px solid #eee;
}

.value-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.value-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.currency {
  font-size: 16px;
  margin-right: 4px;
}

.value-change {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.value-change i {
  margin-right: 4px;
}

.increased {
  color: #F53F3F;
}

.decreased {
  color: #00B42A;
}

.exposure-metrics {
  display: flex;
  flex: 1;
  justify-content: space-around;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.metric-ratio {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.progress-bar {
  width: 80px;
  height: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #00B42A;
  border-radius: 4px;
}

.ratio-text {
  font-size: 14px;
  color: #00B42A;
  font-weight: 500;
}

.metric-comment {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.metric-comment i {
  margin-right: 4px;
  font-size: 12px;
}

.chart-section {
  display: flex;
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.chart-col {
  flex: 1;
  padding: 0 16px;
}

.chart-title {
  text-align: center;
  margin-bottom: 16px;
  font-size: 16px;
  color: #333;
}

.chart-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.risk-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 24px;
}

.hedging-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.hedging-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

.hedging-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.hedging-title {
  font-weight: bold;
  display: flex;
  align-items: center;
}

.hedging-title i {
  margin-right: 8px;
  color: #165DFF;
}

.hedging-priority {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
}

.high-priority {
  background-color: rgba(245, 63, 63, 0.1);
  color: #F53F3F;
}

.medium-priority {
  background-color: rgba(250, 173, 20, 0.1);
  color: #FAAD14;
}

.low-priority {
  background-color: rgba(134, 144, 156, 0.1);
  color: #86909C;
}

.hedging-description {
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.hedging-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.hedging-metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 13px;
  color: #666;
}

.metric-value {
  font-weight: 500;
}

.hedging-actions {
  display: flex;
  gap: 8px;
}

.positive {
  color: #00B42A;
}

.negative {
  color: #F53F3F;
}

.impact-level {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  display: inline-block;
  text-align: center;
}

.impact-low {
  background-color: rgba(0, 180, 42, 0.1);
  color: #00B42A;
}

.impact-medium {
  background-color: rgba(250, 173, 20, 0.1);
  color: #FAAD14;
}

.impact-high {
  background-color: rgba(245, 63, 63, 0.1);
  color: #F53F3F;
}

.impact-severe {
  background-color: rgba(204, 0, 0, 0.1);
  color: #CC0000;
}

@media (max-width: 1200px) {
  .summary-header {
    flex-direction: column;
  }

  .exposure-value {
    padding-right: 0;
    border-right: none;
    border-bottom: 1px solid #eee;
    padding-bottom: 16px;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .chart-section {
    flex-direction: column;
  }

  .chart-col {
    margin-bottom: 24px;
  }

  .risk-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .exposure-metrics {
    flex-direction: column;
    gap: 16px;
  }

  .hedging-metrics {
    flex-direction: column;
    gap: 8px;
  }

  .hedging-actions {
    flex-direction: column;
  }
}
</style>

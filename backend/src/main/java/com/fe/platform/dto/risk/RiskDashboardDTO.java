package com.fe.platform.dto.risk;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 风险仪表盘DTO
 */
@Data
public class RiskDashboardDTO {
    
    /**
     * 概览数据
     */
    private OverviewDTO overview;
    
    /**
     * 汇率变化分析数据
     */
    private ExchangeRateAnalysisDTO exchangeRateAnalysis;
    
    /**
     * 风险模型数据
     */
    private List<RiskModelDTO> riskModels;
    
    /**
     * 敞口分析数据
     */
    private ExposureAnalysisDTO exposureAnalysis;
    
    /**
     * 结汇建议数据
     */
    private List<RecommendationDTO> recommendations;
    
    /**
     * 概览数据DTO
     */
    @Data
    public static class OverviewDTO {
        /**
         * 净美元敞口
         */
        private BigDecimal netUsdExposure;
        private BigDecimal netUsdExposureChange;
        private Boolean netUsdExposureDecreased;
        
        /**
         * 汇率波动率
         */
        private BigDecimal volatility;
        private BigDecimal volatilityChange;
        private Boolean volatilityIncreased;
        
        /**
         * VaR值
         */
        private BigDecimal var;
        private BigDecimal varChange;
        private Boolean varIncreased;
        
        /**
         * 风险等级
         */
        private String riskLevel;
        private Integer riskScore;
    }
    
    /**
     * 汇率变化分析DTO
     */
    @Data
    public static class ExchangeRateAnalysisDTO {
        /**
         * 图表数据
         */
        private List<String> dates;
        private Map<String, List<BigDecimal>> rates;
        
        /**
         * 关键指标
         */
        private Map<String, BigDecimal> keyIndicators;
        
        /**
         * 相关性分析
         */
        private List<CorrelationDTO> correlations;
    }
    
    /**
     * 相关性DTO
     */
    @Data
    public static class CorrelationDTO {
        private String pair;
        private BigDecimal value;
    }
    
    /**
     * 风险模型DTO
     */
    @Data
    public static class RiskModelDTO {
        private String name;
        private String description;
        private BigDecimal value;
        private String impact;
        private String trend;
        private List<HistoricalDataDTO> historicalData;
        private Map<String, Object> detailedMetrics; // 详细指标
    }
    
    /**
     * 历史数据DTO
     */
    @Data
    public static class HistoricalDataDTO {
        private String date;
        private BigDecimal value;
    }
    
    /**
     * 敞口分析DTO
     */
    @Data
    public static class ExposureAnalysisDTO {
        /**
         * 货币敞口
         */
        private List<CurrencyExposureDTO> currencyExposures;
        
        /**
         * 敞口时间分布
         */
        private List<ExposureTimeDTO> timeDistribution;
    }
    
    /**
     * 货币敞口DTO
     */
    @Data
    public static class CurrencyExposureDTO {
        private String currency;
        private BigDecimal amount;
        private BigDecimal percentage;
        private BigDecimal riskContribution;
    }
    
    /**
     * 敞口时间分布DTO
     */
    @Data
    public static class ExposureTimeDTO {
        private String period;
        private BigDecimal amount;
        private BigDecimal percentage;
    }
    
    /**
     * 结汇建议DTO
     */
    @Data
    public static class RecommendationDTO {
        private String type;
        private String currency;
        private BigDecimal amount;
        private String timeframe;
        private String reason;
        private BigDecimal expectedBenefit;
        private BigDecimal potentialRisk;
    }
} 
